import { contextBridge, ipc<PERSON>enderer } from 'electron'

// Custom APIs for renderer
const api = {
  ping: () => ipcRenderer.invoke('ping'),
  
  // Authentication APIs
  auth: {
    login: (forceNewLogin?: boolean) => ipcRenderer.invoke('auth:login', forceNewLogin),
    logout: () => ipcRenderer.invoke('auth:logout'),
    autoLogin: () => ipcRenderer.invoke('auth:auto-login'),
  },

  // Account APIs
  accounts: {
    getAll: () => ipcRenderer.invoke('accounts:get-all'),
    getActive: () => ipcRenderer.invoke('accounts:get-active'),
    switch: (accountId: string) => ipcRenderer.invoke('accounts:switch', accountId),
    remove: (accountId: string) => ipcRenderer.invoke('accounts:remove', accountId),
    clearAll: () => ipcRenderer.invoke('accounts:clear-all'),
  },

  // User APIs
  user: {
    getStorageInfo: () => ipcRenderer.invoke('user:get-storage-info'),
    getProfile: () => ipc<PERSON><PERSON><PERSON>.invoke('user:get-profile'),
    getPhoto: (size?: string) => ipcRenderer.invoke('user:get-photo', size),
  },


  
  // File management APIs
  files: {
    list: (path?: string) => ipcRenderer.invoke('files:list', path),
    createFolder: (name: string, parentPath?: string) => ipcRenderer.invoke('files:create-folder', name, parentPath),
    upload: (filePath: string, fileName: string, parentPath?: string) => ipcRenderer.invoke('files:upload', filePath, fileName, parentPath),
    uploadBuffer: (fileData: number[], fileName: string, parentPath?: string) => ipcRenderer.invoke('files:upload-buffer', fileData, fileName, parentPath),
    download: (itemId: string, localPath: string) => ipcRenderer.invoke('files:download', itemId, localPath),
    delete: (itemId: string) => ipcRenderer.invoke('files:delete', itemId),
    search: (query: string) => ipcRenderer.invoke('files:search', query),
  },
  
  // Dialog APIs
  showSaveDialog: (options: any) => ipcRenderer.invoke('dialog:showSaveDialog', options),
  showOpenDialog: (options: any) => ipcRenderer.invoke('dialog:showOpenDialog', options),
  
  // Config APIs
  config: {
    get: () => ipcRenderer.invoke('config:get'),
    update: (updates: any) => ipcRenderer.invoke('config:update', updates),
    reset: () => ipcRenderer.invoke('config:reset'),
    getPath: () => ipcRenderer.invoke('config:get-path'),
    setCustomPath: (customPath: string) => ipcRenderer.invoke('config:set-custom-path', customPath),
    export: (exportPath: string) => ipcRenderer.invoke('config:export', exportPath),
    import: (importPath: string) => ipcRenderer.invoke('config:import', importPath),
    clearCache: () => ipcRenderer.invoke('config:clear-cache')
  },
}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electronAPI', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electronAPI = api
} 