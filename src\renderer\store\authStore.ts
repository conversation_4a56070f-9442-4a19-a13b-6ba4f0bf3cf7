import { create } from 'zustand'

interface User {
  id: string
  displayName: string
  userPrincipalName: string
  mail?: string
}

interface AuthState {
  isAuthenticated: boolean
  user: User | null
  accessToken: string | null
  isAutoLogin: boolean
  login: (forceNewLogin?: boolean) => Promise<boolean>
  logout: () => Promise<void>
  autoLogin: () => Promise<boolean>
  setUser: (user: User, token: string) => void
  syncUserFromActiveAccount: () => Promise<void>
}

export const useAuthStore = create<AuthState>((set, get) => ({
  isAuthenticated: false,
  user: null,
  accessToken: null,
  isAutoLogin: false,

  login: async (forceNewLogin: boolean = false) => {
    try {
      const result = await window.electronAPI.auth.login(forceNewLogin)
      
      if (result.success && result.userInfo && result.accessToken) {
        set({
          isAuthenticated: true,
          user: {
            id: result.userInfo.id,
            displayName: result.userInfo.displayName || result.userInfo.name,
            userPrincipalName: result.userInfo.userPrincipalName || result.userInfo.email,
            mail: result.userInfo.mail || result.userInfo.email
          },
          accessToken: result.accessToken
        })
        return true
      } else {
        throw new Error(result.error || 'Login failed')
      }
    } catch (error) {
      console.error('Login error:', error)
      throw error
    }
  },

  logout: async () => {
    try {
      const result = await window.electronAPI.auth.logout()
      
      if (result.success) {
        set({
          isAuthenticated: false,
          user: null,
          accessToken: null,
          isAutoLogin: false
        })
      } else {
        throw new Error(result.error || 'Logout failed')
      }
    } catch (error) {
      console.error('Logout error:', error)
      throw error
    }
  },

  autoLogin: async () => {
    try {
      const result = await window.electronAPI.auth.autoLogin()
      
      if (result.success && result.userInfo && result.accessToken) {
        set({
          isAuthenticated: true,
          user: {
            id: result.userInfo.id,
            displayName: result.userInfo.displayName || result.userInfo.name,
            userPrincipalName: result.userInfo.userPrincipalName || result.userInfo.email,
            mail: result.userInfo.mail || result.userInfo.email
          },
          accessToken: result.accessToken,
          isAutoLogin: true
        })
        return true
      } else {
        console.log('Auto login failed:', result.error)
        return false
      }
    } catch (error) {
      console.error('Auto login error:', error)
      return false
    }
  },

  setUser: (user: User, token: string) => {
    set({
      isAuthenticated: true,
      user,
      accessToken: token
    })
  },

  syncUserFromActiveAccount: async () => {
    try {
      // 获取后端的活跃账号
      const result = await window.electronAPI.accounts.getActive()
      
      if (result.success && result.account) {
        // 更新authStore状态以匹配后端的活跃账号
        set({
          isAuthenticated: true,
          user: {
            id: result.account.id,
            displayName: result.account.displayName,
            userPrincipalName: result.account.userPrincipalName,
            mail: result.account.mail
          }
          // 保持现有的accessToken不变，因为后端已经设置了正确的token
        })
      } else {
        // 如果没有活跃账号，清除认证状态
        set({
          isAuthenticated: false,
          user: null,
          accessToken: null,
          isAutoLogin: false
        })
      }
    } catch (error) {
      console.error('Failed to sync user state:', error)
    }
  }
})) 