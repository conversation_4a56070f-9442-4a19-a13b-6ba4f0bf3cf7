import { AccountService } from './accountService'
import { ConfigService } from './configService'
import { AuthService } from './authService'

/**
 * 统一状态管理器
 * 负责协调 AccountService、ConfigService 和 AuthService 之间的状态同步
 */
export class StateManager {
  private accountService: AccountService
  private configService: ConfigService
  private authService: AuthService

  constructor(
    accountService: AccountService,
    configService: ConfigService,
    authService: AuthService
  ) {
    this.accountService = accountService
    this.configService = configService
    this.authService = authService

    // 监听账户服务的状态变化
    this.accountService.onStateChange(this.handleAccountStateChange.bind(this))
  }

  /**
   * 处理账户状态变化事件
   */
  private handleAccountStateChange(event: string, data: any): void {
    console.log('Account state change:', event, data)

    switch (event) {
      case 'account-added':
        this.syncConfigAfterAccountAdded(data)
        break
      case 'account-switched':
        this.syncConfigAfterAccountSwitched(data)
        break
      case 'account-removed':
        this.syncConfigAfterAccountRemoved(data)
        break
      case 'accounts-cleared':
        this.syncConfigAfterAccountsCleared()
        break
    }
  }

  /**
   * 添加账户后同步配置
   */
  private syncConfigAfterAccountAdded(accountId: string): void {
    try {
      // 新添加的账户自动成为当前账户
      this.configService.updateConfig({ currentAccountId: accountId })
      console.log('Config synced after account added:', accountId)
    } catch (error) {
      console.error('Failed to sync config after account added:', error)
    }
  }

  /**
   * 切换账户后同步配置
   */
  private syncConfigAfterAccountSwitched(accountId: string): void {
    try {
      // 更新配置中的当前账户ID
      this.configService.updateConfig({ currentAccountId: accountId })
      console.log('Config synced after account switched:', accountId)
    } catch (error) {
      console.error('Failed to sync config after account switched:', error)
    }
  }

  /**
   * 删除账户后同步配置
   */
  private syncConfigAfterAccountRemoved(data: { accountId: string; wasActive: boolean }): void {
    try {
      const { accountId, wasActive } = data
      
      if (wasActive) {
        // 如果删除的是活跃账户，清除当前账户ID
        this.configService.updateConfig({ currentAccountId: undefined })
        console.log('Config synced after active account removed:', accountId)
      }
    } catch (error) {
      console.error('Failed to sync config after account removed:', error)
    }
  }

  /**
   * 清除所有账户后同步配置
   */
  private syncConfigAfterAccountsCleared(): void {
    try {
      // 清除当前账户ID
      this.configService.updateConfig({ currentAccountId: undefined })
      console.log('Config synced after all accounts cleared')
    } catch (error) {
      console.error('Failed to sync config after accounts cleared:', error)
    }
  }

  /**
   * 执行完整的状态同步
   * 确保 AccountService 和 ConfigService 的状态一致
   */
  public syncAllStates(): void {
    try {
      const accountSnapshot = this.accountService.getStateSnapshot()
      const config = this.configService.getConfig()

      console.log('Current state snapshot:', {
        activeAccountId: accountSnapshot.activeAccountId,
        configCurrentAccountId: config.currentAccountId,
        accountsCount: accountSnapshot.accounts.length
      })

      // 检查状态是否一致
      if (accountSnapshot.activeAccountId !== config.currentAccountId) {
        console.log('State inconsistency detected, syncing...')
        
        if (accountSnapshot.activeAccountId) {
          // AccountService 有活跃账户，更新配置
          this.configService.updateConfig({ 
            currentAccountId: accountSnapshot.activeAccountId 
          })
          console.log('Config updated to match active account:', accountSnapshot.activeAccountId)
        } else if (config.currentAccountId) {
          // 配置有当前账户ID但AccountService没有活跃账户
          // 尝试激活配置中的账户
          const success = this.accountService.switchAccount(config.currentAccountId)
          if (!success) {
            // 如果账户不存在，清除配置
            this.configService.updateConfig({ currentAccountId: undefined })
            console.log('Config cleared due to non-existent account:', config.currentAccountId)
          } else {
            console.log('Account activated to match config:', config.currentAccountId)
          }
        }
      } else {
        console.log('States are consistent')
      }
    } catch (error) {
      console.error('Failed to sync all states:', error)
    }
  }

  /**
   * 获取当前的完整状态
   */
  public getCurrentState(): {
    accounts: any[]
    activeAccount: any | null
    activeAccountId: string | null
    configCurrentAccountId: string | undefined
    isAuthenticated: boolean
  } {
    const accountSnapshot = this.accountService.getStateSnapshot()
    const config = this.configService.getConfig()
    
    return {
      accounts: accountSnapshot.accounts,
      activeAccount: accountSnapshot.activeAccount,
      activeAccountId: accountSnapshot.activeAccountId,
      configCurrentAccountId: config.currentAccountId,
      isAuthenticated: !!accountSnapshot.activeAccount
    }
  }

  /**
   * 强制重置所有状态到一致状态
   */
  public resetToConsistentState(): void {
    try {
      console.log('Resetting to consistent state...')
      
      // 清除所有状态
      this.accountService.clearAllAccounts()
      this.configService.updateConfig({ currentAccountId: undefined })
      
      console.log('All states reset to consistent empty state')
    } catch (error) {
      console.error('Failed to reset to consistent state:', error)
    }
  }
}
