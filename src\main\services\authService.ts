import { PublicClientApplication, Configuration, AuthenticationResult } from '@azure/msal-node'
import { BrowserWindow, shell, dialog } from 'electron'

export interface AuthConfig {
  clientId: string
  authority: string
  redirectUri: string
}

export class AuthService {
  private msalApp: PublicClientApplication
  private config: AuthConfig
  private currentAccessToken: string | null = null

  constructor(config: AuthConfig) {
    this.config = config
    
    const msalConfig: Configuration = {
      auth: {
        clientId: config.clientId,
        authority: config.authority,
      },
      system: {
        loggerOptions: {
          loggerCallback: (level, message, containsPii) => {
            if (level <= 2) { // Only log errors
              console.log(`[MSAL] ${message}`)
            }
          },
          piiLoggingEnabled: false,
          logLevel: 1, // Error level only
        },
      },
    }

    this.msalApp = new PublicClientApplication(msalConfig)
  }

  async authenticate(forceNewLogin: boolean = false): Promise<AuthenticationResult> {
    const scopes = [
      'https://graph.microsoft.com/Files.ReadWrite.All',
      'https://graph.microsoft.com/User.Read',
      'offline_access'
    ]

    try {
      // 如果不是强制新登录，尝试静默获取令牌
      if (!forceNewLogin) {
        const accounts = await this.msalApp.getTokenCache().getAllAccounts()
        if (accounts.length > 0) {
          const silentRequest = {
            account: accounts[0],
            scopes: scopes,
          }

          try {
            const response = await this.msalApp.acquireTokenSilent(silentRequest)
            console.log('Silent token acquisition successful')
            return response
          } catch (error) {
            console.log('Silent token acquisition failed, starting device code flow')
          }
        }
      } else {
        console.log('Force new login requested, starting device code flow')
      }

      // Use device code flow with improved UI
      return await this.authenticateWithDeviceCode(scopes)
    } catch (error) {
      console.error('Authentication failed:', error)
      throw error
    }
  }

  private async authenticateWithDeviceCode(scopes: string[]): Promise<AuthenticationResult> {
    return new Promise((resolve, reject) => {
      const deviceCodeRequest = {
        scopes: scopes,
        deviceCodeCallback: (response: any) => {
          console.log('Device code received:', response.userCode)
          this.showDeviceCodeDialog(response.userCode, response.verificationUri)
        },
      }

      this.msalApp.acquireTokenByDeviceCode(deviceCodeRequest)
        .then(resolve)
        .catch((error) => {
          console.error('Device code authentication failed:', error)
          // Show user-friendly error message
          dialog.showErrorBox(
            'Authentication Failed', 
            `Failed to authenticate with Microsoft: ${error.message || 'Unknown error'}\n\nPlease try again.`
          )
          reject(error)
        })
    })
  }

  private showDeviceCodeDialog(userCode: string, verificationUri: string) {
    // Show a native dialog first
    const choice = dialog.showMessageBoxSync(null, {
      type: 'info',
      title: 'OneDrive Authentication',
      message: 'Authentication Required',
      detail: `To sign in to OneDrive, please:\n\n1. Open your web browser\n2. Go to: ${verificationUri}\n3. Enter this code: ${userCode}\n\nClick "Open Browser" to open the verification page automatically.`,
      buttons: ['Open Browser', 'Copy Code', 'Cancel'],
      defaultId: 0,
      cancelId: 2
    })

    if (choice === 0) {
      // Open browser
      shell.openExternal(verificationUri)
    } else if (choice === 1) {
      // Copy code to clipboard
      require('electron').clipboard.writeText(userCode)
      dialog.showMessageBoxSync(null, {
        type: 'info',
        title: 'Code Copied',
        message: `Code "${userCode}" has been copied to clipboard.\n\nPlease open ${verificationUri} in your browser and paste the code.`,
        buttons: ['OK']
      })
    }

    // Also create a small window with the code for reference
    const codeWindow = new BrowserWindow({
      width: 400,
      height: 300,
      resizable: false,
      minimizable: false,
      maximizable: false,
      alwaysOnTop: true,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
      },
    })

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Authentication Code</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px;
            text-align: center;
            background: #f5f5f5;
            margin: 0;
          }
          .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          }
          .code {
            font-size: 20px;
            font-weight: bold;
            color: #0078d4;
            background: #f0f8ff;
            padding: 10px;
            border-radius: 4px;
            margin: 15px 0;
            letter-spacing: 1px;
            border: 2px solid #0078d4;
          }
          .url {
            font-size: 12px;
            color: #666;
            margin: 10px 0;
            word-break: break-all;
          }
          .instruction {
            font-size: 14px;
            color: #333;
            margin: 10px 0;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h3>OneDrive Authentication</h3>
          <div class="instruction">Enter this code in your browser:</div>
          <div class="code">${userCode}</div>
          <div class="url">${verificationUri}</div>
          <div class="instruction">This window will close automatically after authentication</div>
        </div>
      </body>
      </html>
    `

    codeWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`)
    
    // Auto-close after 10 minutes
    setTimeout(() => {
      if (!codeWindow.isDestroyed()) {
        codeWindow.close()
      }
    }, 600000)
  }

  async logout(): Promise<void> {
    const accounts = await this.msalApp.getTokenCache().getAllAccounts()
    for (const account of accounts) {
      await this.msalApp.getTokenCache().removeAccount(account)
    }
  }

  async getAccessToken(): Promise<string | null> {
    // 如果有当前令牌，直接返回
    if (this.currentAccessToken) {
      return this.currentAccessToken
    }

    try {
      const accounts = await this.msalApp.getTokenCache().getAllAccounts()
      if (accounts.length === 0) {
        return null
      }

      const silentRequest = {
        account: accounts[0],
        scopes: ['https://graph.microsoft.com/Files.ReadWrite.All'],
      }

      const response = await this.msalApp.acquireTokenSilent(silentRequest)
      this.currentAccessToken = response.accessToken
      return response.accessToken
    } catch (error) {
      console.error('Failed to get access token:', error)
      return null
    }
  }

  // 设置访问令牌（用于多账号切换）
  setAccessToken(accessToken: string): void {
    this.currentAccessToken = accessToken
  }

  // 清除当前访问令牌
  clearAccessToken(): void {
    this.currentAccessToken = null
  }

  // 刷新令牌
  async refreshToken(refreshToken: string): Promise<{ accessToken: string; refreshToken?: string; expiresAt?: number }> {
    try {
      // 注意：MSAL Node 通常不直接支持刷新令牌操作
      // 这里我们尝试使用静默获取令牌的方式
      const accounts = await this.msalApp.getTokenCache().getAllAccounts()
      
      if (accounts.length > 0) {
        const silentRequest = {
          account: accounts[0],
          scopes: [
            'https://graph.microsoft.com/Files.ReadWrite.All',
            'https://graph.microsoft.com/User.Read',
            'offline_access'
          ],
          forceRefresh: true // 强制刷新
        }

        const response = await this.msalApp.acquireTokenSilent(silentRequest)
        
        return {
          accessToken: response.accessToken,
          refreshToken: refreshToken, // 保持原有的刷新令牌
          expiresAt: response.expiresOn ? response.expiresOn.getTime() : undefined
        }
      }
      
      throw new Error('No accounts available for token refresh')
    } catch (error) {
      console.error('Failed to refresh token:', error)
      throw new Error('令牌刷新失败')
    }
  }
} 