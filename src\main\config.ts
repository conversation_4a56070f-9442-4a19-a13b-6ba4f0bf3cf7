export const APP_CONFIG = {
  // Microsoft App Registration details
  // Using Microsoft Graph Explorer's public client ID for testing
  auth: {
    clientId: '14d82eec-204b-4c2f-b7e8-296a70dab67e', // Microsoft Graph Explorer public client ID
    authority: 'https://login.microsoftonline.com/common',
    redirectUri: 'msal14d82eec-204b-4c2f-b7e8-296a70dab67e://auth' // Native redirect URI
  },
  
  // Application settings
  app: {
    name: 'OD-Commander',
    version: '1.0.0',
    description: 'A modern OneDrive client for Windows'
  },
  
  // OneDrive API scopes
  scopes: [
    'https://graph.microsoft.com/Files.ReadWrite.All',
    'https://graph.microsoft.com/User.Read',
    'offline_access'
  ]
}

// Instructions for setting up your own Microsoft App Registration:
/*
1. Go to https://portal.azure.com/#blade/Microsoft_AAD_RegisteredApps/ApplicationsListBlade
2. Click "New registration"
3. Enter application name: "OD-Commander"
4. Select "Accounts in any organizational directory and personal Microsoft accounts"
5. For redirect URI, select "Public client/native (mobile & desktop)" and enter: "msal[your-client-id]://auth"
6. Click "Register"
7. Copy the "Application (client) ID" and replace the clientId above
8. Go to "Authentication" tab:
   - Enable "Allow public client flows" (set to Yes)
   - Add redirect URI: "msal[your-client-id]://auth" as Mobile and desktop applications
9. Go to "API permissions" tab and add Microsoft Graph permissions:
   - Files.ReadWrite.All (Delegated)
   - User.Read (Delegated)
   - offline_access (Delegated)
10. Click "Grant admin consent" if you're an admin, or users will need to consent individually

Note: Currently using Microsoft Graph Explorer's public client ID for testing.
For production, you should register your own application.
*/ 