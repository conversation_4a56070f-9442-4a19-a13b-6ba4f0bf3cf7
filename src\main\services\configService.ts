import { app } from 'electron'
import * as fs from 'fs'
import * as path from 'path'

export interface AppConfig {
  // 通用设置
  theme: 'auto' | 'light' | 'dark'
  language: 'zh-CN' | 'en-US'
  startWithWindows: boolean
  minimizeToTray: boolean
  showNotifications: boolean
  
  // 同步设置
  autoSync: boolean
  syncInterval: number // 分钟
  downloadPath: string
  uploadQuality: 'original' | 'compressed'
  conflictResolution: 'ask' | 'overwrite' | 'skip'
  
  // 高级设置
  maxConcurrentUploads: number
  maxConcurrentDownloads: number
  cacheSize: number // MB
  enableLogging: boolean
  
  // 开发者设置
  developerMode: boolean
  
  // 账号设置
  currentAccountId?: string
  rememberLogin: boolean
  
  // 窗口设置
  windowBounds: {
    width: number
    height: number
    x?: number
    y?: number
  }
  
  // 自定义配置路径
  customConfigPath?: string
}

export class ConfigService {
  private configPath: string
  private config: AppConfig
  private defaultConfig: AppConfig = {
    theme: 'auto',
    language: 'zh-CN',
    startWithWindows: false,
    minimizeToTray: true,
    showNotifications: true,
    autoSync: true,
    syncInterval: 30,
    downloadPath: path.join(app.getPath('downloads'), 'OneDrive'),
    uploadQuality: 'original',
    conflictResolution: 'ask',
    maxConcurrentUploads: 3,
    maxConcurrentDownloads: 5,
    cacheSize: 500,
    enableLogging: true,
    developerMode: false,
    rememberLogin: true,
    windowBounds: {
      width: 1200,
      height: 800
    }
  }

  constructor() {
    this.configPath = this.getDefaultConfigPath()
    this.config = this.loadConfig()
  }

  private getDefaultConfigPath(): string {
    // 默认使用 AppData/Roaming/OD-Commander
    const userDataPath = app.getPath('userData')
    const configDir = path.join(userDataPath, 'config')
    
    // 确保配置目录存在
    if (!fs.existsSync(configDir)) {
      fs.mkdirSync(configDir, { recursive: true })
    }
    
    return path.join(configDir, 'app-config.json')
  }

  private getCustomConfigPath(customPath?: string): string {
    if (customPath && fs.existsSync(customPath)) {
      return path.join(customPath, 'od-commander-config.json')
    }
    
    return this.getDefaultConfigPath()
  }

  private loadConfig(): AppConfig {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readFileSync(this.configPath, 'utf-8')
        const loadedConfig = JSON.parse(configData)
        
        // 合并默认配置和加载的配置，确保所有字段都存在
        return { ...this.defaultConfig, ...loadedConfig }
      }
    } catch (error) {
      console.error('加载配置文件失败:', error)
    }
    
    // 如果加载失败或文件不存在，返回默认配置
    return { ...this.defaultConfig }
  }

  public saveConfig(): void {
    try {
      const configDir = path.dirname(this.configPath)
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true })
      }
      
      fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2), 'utf-8')
      console.log('配置已保存到:', this.configPath)
    } catch (error) {
      console.error('保存配置文件失败:', error)
      throw new Error('保存配置失败')
    }
  }

  public getConfig(): AppConfig {
    return { ...this.config }
  }

  public updateConfig(updates: Partial<AppConfig>): void {
    this.config = { ...this.config, ...updates }
    this.saveConfig()
  }

  public resetConfig(): void {
    this.config = { ...this.defaultConfig }
    this.saveConfig()
  }

  public getConfigPath(): string {
    return this.configPath || this.getDefaultConfigPath()
  }

  public setCustomConfigPath(customPath: string): void {
    if (!fs.existsSync(customPath)) {
      throw new Error('指定的配置路径不存在')
    }
    
    // 备份当前配置
    const currentConfig = this.getConfig()
    
    // 更新配置路径
    this.configPath = this.getCustomConfigPath(customPath)
    this.config.customConfigPath = customPath
    
    // 保存到新路径
    this.saveConfig()
    
    console.log('配置路径已更改为:', this.configPath)
  }

  // 创建下载目录
  public ensureDownloadPath(): void {
    const downloadPath = this.config.downloadPath
    if (!fs.existsSync(downloadPath)) {
      try {
        fs.mkdirSync(downloadPath, { recursive: true })
        console.log('创建下载目录:', downloadPath)
      } catch (error) {
        console.error('创建下载目录失败:', error)
        // 如果创建失败，回退到默认下载目录
        this.config.downloadPath = this.defaultConfig.downloadPath
        this.saveConfig()
      }
    }
  }

  // 获取缓存目录
  public getCacheDir(): string {
    const cacheDir = path.join(app.getPath('userData'), 'cache')
    if (!fs.existsSync(cacheDir)) {
      fs.mkdirSync(cacheDir, { recursive: true })
    }
    return cacheDir
  }

  // 清理缓存
  public clearCache(): void {
    const cacheDir = this.getCacheDir()
    try {
      if (fs.existsSync(cacheDir)) {
        fs.rmSync(cacheDir, { recursive: true, force: true })
        fs.mkdirSync(cacheDir, { recursive: true })
        console.log('缓存已清理')
      }
    } catch (error) {
      console.error('清理缓存失败:', error)
    }
  }

  // 获取日志目录
  public getLogDir(): string {
    const logDir = path.join(app.getPath('userData'), 'logs')
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true })
    }
    return logDir
  }

  // 导出配置
  public exportConfig(exportPath: string): void {
    try {
      const configData = JSON.stringify(this.config, null, 2)
      fs.writeFileSync(exportPath, configData, 'utf-8')
      console.log('配置已导出到:', exportPath)
    } catch (error) {
      console.error('导出配置失败:', error)
      throw new Error('导出配置失败')
    }
  }

  // 导入配置
  public importConfig(importPath: string): void {
    try {
      if (!fs.existsSync(importPath)) {
        throw new Error('配置文件不存在')
      }
      
      const configData = fs.readFileSync(importPath, 'utf-8')
      const importedConfig = JSON.parse(configData)
      
      // 验证配置格式
      const validatedConfig = { ...this.defaultConfig, ...importedConfig }
      
      this.config = validatedConfig
      this.saveConfig()
      console.log('配置已导入')
    } catch (error) {
      console.error('导入配置失败:', error)
      throw new Error('导入配置失败')
    }
  }
} 