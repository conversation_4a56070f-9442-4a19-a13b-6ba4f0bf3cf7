import React, { useState, useEffect } from 'react'
import { 
  Settings as SettingsIcon, 
  User, 
  Monitor, 
  Download, 
  Upload, 
  Folder, 
  Bell, 
  Shield, 
  Info,
  LogOut,
  Save,
  RotateCcw,
  FolderOpen,
  FileDown,
  FileUp,
  Trash2,
  RefreshCw,
  Plus,
  User<PERSON>heck,
  UserX,
  Users
} from 'lucide-react'
import { useConfigStore, AppConfig } from '../store/configStore'
import { useAuthStore } from '../store/authStore'
import { useUserStore, formatBytes, getStoragePercentage } from '../store/userStore'
import { useAccountStore } from '../store/accountStore'
import { useStateSync } from '../hooks/useStateSync'

interface SettingsProps {
  isVisible: boolean
  onClose: () => void
}

const Settings: React.FC<SettingsProps> = ({ isVisible, onClose }) => {
  const [activeTab, setActiveTab] = useState<'general' | 'sync' | 'account' | 'about'>('general')
  const [configPath, setConfigPath] = useState<string>('')
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  
  const { 
    config, 
    isLoading, 
    error, 
    loadConfig, 
    updateConfig, 
    resetConfig, 
    getConfigPath,
    exportConfig,
    importConfig,
    clearCache
  } = useConfigStore()
  
  const { user, logout, login, syncUserFromActiveAccount } = useAuthStore()
  const { 
    profile, 
    storageInfo, 
    isLoading: userLoading, 
    loadUserData, 
    loadUserPhoto,
    clearUserData 
  } = useUserStore()
  const {
    accounts,
    activeAccount,
    isLoading: accountsLoading,
    loadAccounts,
    switchAccount,
    removeAccount,
    clearAllAccounts
  } = useAccountStore()

  // 加载配置和配置路径
  useEffect(() => {
    if (isVisible) {
      loadConfig()
      loadUserData() // 加载用户数据
      loadAccounts() // 加载账号列表
      getConfigPath().then(setConfigPath).catch(console.error)
    }
  }, [isVisible, loadConfig, loadUserData, loadAccounts, getConfigPath])

  // 显示消息
  const showMessage = (type: 'success' | 'error', text: string) => {
    setMessage({ type, text })
    setTimeout(() => setMessage(null), 3000)
  }

  const handleSettingChange = async (key: string, value: any) => {
    try {
      await updateConfig({ [key]: value })
      showMessage('success', '设置已保存')
    } catch (error) {
      showMessage('error', '保存设置失败')
    }
  }

  const handleSave = () => {
    showMessage('success', '所有设置已保存')
    onClose()
  }

  const handleReset = async () => {
    if (confirm('确定要重置所有设置为默认值吗？')) {
      try {
        await resetConfig()
        showMessage('success', '设置已重置为默认值')
      } catch (error) {
        showMessage('error', '重置设置失败')
      }
    }
  }

  const handleSelectDownloadPath = async () => {
    try {
      const result = await window.electronAPI.showOpenDialog({
        properties: ['openDirectory'],
        title: '选择下载文件夹'
      })
      
      if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
        await handleSettingChange('downloadPath', result.filePaths[0])
      }
    } catch (error) {
      showMessage('error', '选择文件夹失败')
    }
  }

  const handleExportConfig = async () => {
    try {
      const result = await window.electronAPI.showSaveDialog({
        title: '导出配置',
        defaultPath: 'od-commander-config.json',
        filters: [
          { name: 'JSON文件', extensions: ['json'] }
        ]
      })
      
      if (!result.canceled && result.filePath) {
        await exportConfig(result.filePath)
        showMessage('success', '配置已导出')
      }
    } catch (error) {
      showMessage('error', '导出配置失败')
    }
  }

  const handleImportConfig = async () => {
    try {
      const result = await window.electronAPI.showOpenDialog({
        title: '导入配置',
        filters: [
          { name: 'JSON文件', extensions: ['json'] }
        ],
        properties: ['openFile']
      })
      
      if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
        await importConfig(result.filePaths[0])
        showMessage('success', '配置已导入')
      }
    } catch (error) {
      showMessage('error', '导入配置失败')
    }
  }

  const handleClearCache = async () => {
    if (confirm('确定要清理所有缓存吗？')) {
      try {
        await clearCache()
        showMessage('success', '缓存已清理')
      } catch (error) {
        showMessage('error', '清理缓存失败')
      }
    }
  }

  const handleLogout = async () => {
    if (confirm('确定要退出登录吗？')) {
      try {
        await logout()
        clearUserData() // 清理用户数据
        onClose()
      } catch (error) {
        showMessage('error', '退出登录失败')
      }
    }
  }

  const handleRefreshPhoto = async () => {
    try {
      const photo = await loadUserPhoto('120x120')
      if (photo) {
        showMessage('success', '头像刷新成功')
      } else {
        showMessage('error', '无法获取用户头像')
      }
    } catch (error) {
      showMessage('error', '刷新头像失败')
    }
  }

  const handleAddAccount = async () => {
    try {
      // 强制显示新的登录对话框，即使当前没有认证状态
      console.log('Starting to add new account...')
      const result = await login(true) // 强制显示新的登录对话框
      if (result) {
        console.log('Login successful, refreshing account data...')
        await loadAccounts() // 重新加载账号列表
        await loadUserData() // 重新加载用户数据
        await loadUserPhoto() // 重新加载用户头像
        await syncUserFromActiveAccount() // 同步用户状态
        showMessage('success', '账号添加成功')
      } else {
        console.log('Login failed or was cancelled')
        showMessage('error', '添加账号失败 - 登录被取消或失败')
      }
    } catch (error) {
      console.error('Error adding account:', error)
      showMessage('error', `添加账号失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  const handleSwitchAccount = async (accountId: string) => {
    try {
      console.log('Switching to account:', accountId)
      const result = await switchAccount(accountId)
      if (result.success) {
        // 同步前端状态，确保所有状态都保持一致
        await syncUserFromActiveAccount()
        
        await loadAccounts() // 重新加载账号列表
        await loadUserData() // 重新加载用户数据
        await loadUserPhoto() // 重新加载用户头像
        showMessage('success', '账号切换成功')
      } else {
        showMessage('error', '账号切换失败')
      }
    } catch (error) {
      console.error('Error switching account:', error)
      showMessage('error', '账号切换失败')
    }
  }

  const handleRemoveAccount = async (accountId: string, accountName: string) => {
    if (confirm(`确定要删除账号 "${accountName}" 吗？`)) {
      try {
        const result = await removeAccount(accountId)
        if (result) {
          await loadAccounts() // 重新加载账号列表
          showMessage('success', '账号删除成功')
        } else {
          showMessage('error', '账号删除失败')
        }
      } catch (error) {
        console.error('Error removing account:', error)
        showMessage('error', '账号删除失败')
      }
    }
  }

  const handleClearAllAccounts = async () => {
    if (confirm('确定要删除所有账号吗？这将退出所有登录状态。')) {
      try {
        const result = await clearAllAccounts()
        if (result) {
          await loadAccounts() // 重新加载账号列表
          await syncUserFromActiveAccount() // 同步状态
          showMessage('success', '所有账号已清除')
          onClose() // 关闭设置窗口
        } else {
          showMessage('error', '清除账号失败')
        }
      } catch (error) {
        console.error('Error clearing accounts:', error)
        showMessage('error', '清除账号失败')
      }
    }
  }



  if (!isVisible) return null

  // 如果配置还没加载完成，显示加载状态
  if (!config) {
    return (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000
      }}>
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '40px',
          textAlign: 'center'
        }}>
          <div>加载配置中...</div>
        </div>
      </div>
    )
  }

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        width: '800px',
        height: '600px',
        display: 'flex',
        overflow: 'hidden',
        boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)'
      }}>
        {/* 侧边栏 */}
        <div style={{
          width: '200px',
          backgroundColor: '#f8f9fa',
          borderRight: '1px solid #e1e1e6',
          padding: '20px 0'
        }}>
          <div style={{
            padding: '0 20px 20px',
            borderBottom: '1px solid #e1e1e6',
            marginBottom: '20px'
          }}>
            <h2 style={{
              fontSize: '18px',
              fontWeight: '600',
              color: '#1d1d1f',
              margin: 0,
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <SettingsIcon size={20} />
              设置
            </h2>
          </div>

          <div style={{ padding: '0 10px' }}>
            {[
              { id: 'general', label: '通用', icon: Monitor },
              { id: 'sync', label: '同步', icon: Upload },
              { id: 'account', label: '账户', icon: User },
              { id: 'about', label: '关于', icon: Info }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  border: 'none',
                  backgroundColor: activeTab === tab.id ? '#007aff' : 'transparent',
                  color: activeTab === tab.id ? 'white' : '#1d1d1f',
                  borderRadius: '8px',
                  marginBottom: '4px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                  transition: 'all 0.2s ease'
                }}
              >
                <tab.icon size={16} />
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* 主内容区 */}
        <div style={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column'
        }}>
          {/* 内容区域 */}
          <div style={{
            flex: 1,
            padding: '30px',
            overflow: 'auto'
          }}>
            {/* 消息提示 */}
            {message && (
              <div style={{
                padding: '12px 16px',
                borderRadius: '8px',
                marginBottom: '20px',
                fontSize: '14px',
                fontWeight: '500',
                backgroundColor: message.type === 'success' ? '#d4edda' : '#f8d7da',
                color: message.type === 'success' ? '#155724' : '#721c24',
                border: `1px solid ${message.type === 'success' ? '#c3e6cb' : '#f5c6cb'}`
              }}>
                {message.text}
              </div>
            )}
            {activeTab === 'general' && (
              <div>
                <h3 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '20px' }}>通用设置</h3>
                
                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '8px' }}>
                    主题
                  </label>
                  <select
                    value={config.theme}
                    onChange={(e) => handleSettingChange('theme', e.target.value)}
                    style={{
                      width: '200px',
                      padding: '8px 12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px'
                    }}
                  >
                    <option value="auto">跟随系统</option>
                    <option value="light">浅色</option>
                    <option value="dark">深色</option>
                  </select>
                </div>

                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '8px' }}>
                    语言
                  </label>
                  <select
                    value={config.language}
                    onChange={(e) => handleSettingChange('language', e.target.value)}
                    style={{
                      width: '200px',
                      padding: '8px 12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px'
                    }}
                  >
                    <option value="zh-CN">简体中文</option>
                    <option value="en-US">English</option>
                  </select>
                </div>

                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <input
                      type="checkbox"
                      checked={config.startWithWindows}
                      onChange={(e) => handleSettingChange('startWithWindows', e.target.checked)}
                    />
                    开机自启动
                  </label>
                </div>

                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <input
                      type="checkbox"
                      checked={config.minimizeToTray}
                      onChange={(e) => handleSettingChange('minimizeToTray', e.target.checked)}
                    />
                    最小化到系统托盘
                  </label>
                </div>

                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <input
                      type="checkbox"
                      checked={config.showNotifications}
                      onChange={(e) => handleSettingChange('showNotifications', e.target.checked)}
                    />
                    显示通知
                  </label>
                </div>

                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <input
                      type="checkbox"
                      checked={config.rememberLogin}
                      onChange={(e) => handleSettingChange('rememberLogin', e.target.checked)}
                    />
                    记住登录状态
                  </label>
                  <div style={{ fontSize: '12px', color: '#6d6d70', marginTop: '4px', marginLeft: '24px' }}>
                    启用后将保存登录信息，下次启动时自动登录
                  </div>
                </div>

                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <input
                      type="checkbox"
                      checked={config.developerMode}
                      onChange={(e) => handleSettingChange('developerMode', e.target.checked)}
                    />
                    开发者模式
                  </label>
                  <div style={{ fontSize: '12px', color: '#6d6d70', marginTop: '4px', marginLeft: '24px' }}>
                    开启后可使用 Ctrl+Shift+I 或 F12 打开开发者工具
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'sync' && (
              <div>
                <h3 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '20px' }}>同步设置</h3>
                
                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <input
                      type="checkbox"
                      checked={config.autoSync}
                      onChange={(e) => handleSettingChange('autoSync', e.target.checked)}
                    />
                    自动同步
                  </label>
                </div>

                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '8px' }}>
                    同步间隔（分钟）
                  </label>
                  <input
                    type="number"
                    value={config.syncInterval}
                    onChange={(e) => handleSettingChange('syncInterval', parseInt(e.target.value) || 30)}
                    min="1"
                    max="1440"
                    style={{
                      width: '120px',
                      padding: '8px 12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px'
                    }}
                  />
                </div>

                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '8px' }}>
                    下载路径
                  </label>
                  <div style={{ display: 'flex', gap: '8px' }}>
                    <input
                      type="text"
                      value={config.downloadPath}
                      onChange={(e) => handleSettingChange('downloadPath', e.target.value)}
                      placeholder="选择下载文件夹..."
                      style={{
                        flex: 1,
                        padding: '8px 12px',
                        border: '1px solid #d1d5db',
                        borderRadius: '6px',
                        fontSize: '14px'
                      }}
                    />
                    <button
                      onClick={handleSelectDownloadPath}
                      style={{
                        padding: '8px 12px',
                        border: '1px solid #007aff',
                        borderRadius: '6px',
                        backgroundColor: 'white',
                        color: '#007aff',
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px'
                      }}
                    >
                      <Folder size={14} />
                      浏览
                    </button>
                  </div>
                </div>

                <div style={{ marginBottom: '24px' }}>
                  <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '8px' }}>
                    冲突处理
                  </label>
                  <select
                    value={config.conflictResolution}
                    onChange={(e) => handleSettingChange('conflictResolution', e.target.value)}
                    style={{
                      width: '200px',
                      padding: '8px 12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px'
                    }}
                  >
                    <option value="ask">询问我</option>
                    <option value="overwrite">覆盖</option>
                    <option value="skip">跳过</option>
                  </select>
                </div>
              </div>
            )}

            {activeTab === 'account' && (
              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
                  <h3 style={{ fontSize: '16px', fontWeight: '600', margin: 0 }}>账户管理</h3>
                  <button
                    onClick={handleAddAccount}
                    style={{
                      padding: '8px 16px',
                      border: 'none',
                      borderRadius: '6px',
                      backgroundColor: '#007aff',
                      color: 'white',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '6px',
                      fontSize: '14px',
                      fontWeight: '500'
                    }}
                  >
                    <Plus size={16} />
                    添加账号
                  </button>
                </div>

                {/* 当前账号信息 */}
                {user && (
                  <div style={{
                    padding: '20px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px',
                    marginBottom: '24px',
                    border: '2px solid #007aff'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
                      <UserCheck size={16} color="#007aff" />
                      <span style={{ fontSize: '14px', fontWeight: '600', color: '#007aff' }}>当前账号</span>
                    </div>
                    
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px' }}>
                      <div style={{ position: 'relative' }}>
                        <div style={{
                          width: '48px',
                          height: '48px',
                          backgroundColor: '#007aff',
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'white',
                          fontSize: '18px',
                          fontWeight: '600',
                          border: '2px solid #e1e1e6'
                        }}>
                          {user.displayName?.charAt(0) || user.userPrincipalName?.charAt(0) || 'U'}
                        </div>
                        
                        <button
                          onClick={handleRefreshPhoto}
                          style={{
                            position: 'absolute',
                            bottom: '-2px',
                            right: '-2px',
                            width: '20px',
                            height: '20px',
                            borderRadius: '50%',
                            border: '1px solid #007aff',
                            backgroundColor: 'white',
                            color: '#007aff',
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '10px'
                          }}
                          title="刷新头像"
                        >
                          <RefreshCw size={10} />
                        </button>
                      </div>
                      <div>
                        <div style={{ fontSize: '16px', fontWeight: '600', marginBottom: '4px' }}>
                          {user.displayName || '用户'}
                        </div>
                        <div style={{ fontSize: '14px', color: '#6d6d70', marginBottom: '2px' }}>
                          {user.mail || user.userPrincipalName || '<EMAIL>'}
                        </div>
                        <div style={{ fontSize: '12px', color: '#8e8e93' }}>
                          ID: {user.id}
                        </div>
                      </div>
                    </div>
                    
                    {/* 存储信息 - 注意：可能与当前用户不匹配 */}
                    {storageInfo && (
                      <>
                        <div style={{ fontSize: '14px', color: '#6d6d70', marginBottom: '16px' }}>
                          <div style={{ marginBottom: '8px' }}>
                            存储类型：{storageInfo.driveType === 'sharepoint' ? 'SharePoint' : 
                                     storageInfo.driveType === 'personal' ? '个人OneDrive' : 
                                     storageInfo.driveType || '云存储'}
                          </div>
                          {profile && profile.id !== user.id && (
                            <div style={{ color: '#ff9500', fontSize: '12px', marginBottom: '8px' }}>
                              ⚠️ 存储信息可能与当前账号不匹配
                            </div>
                          )}
                        </div>
                        
                        {storageInfo.quota && storageInfo.quota.total > 0 ? (
                          <>
                            <div style={{ 
                              width: '100%', 
                              height: '8px', 
                              backgroundColor: '#e1e1e6', 
                              borderRadius: '4px',
                              marginBottom: '8px'
                            }}>
                              <div style={{
                                width: `${getStoragePercentage(storageInfo)}%`,
                                height: '100%',
                                backgroundColor: getStoragePercentage(storageInfo) > 80 ? '#ff3b30' : '#007aff',
                                borderRadius: '4px',
                                transition: 'all 0.3s ease'
                              }} />
                            </div>
                            
                            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                              <span style={{ fontSize: '14px', color: '#1d1d1f' }}>
                                已使用 {formatBytes(storageInfo.quota.used)} / {formatBytes(storageInfo.quota.total)}
                              </span>
                            </div>
                            
                            <div style={{ 
                              fontSize: '12px', 
                              color: '#8e8e93', 
                              marginTop: '8px',
                              display: 'flex',
                              justifyContent: 'space-between'
                            }}>
                              <span>已使用 {getStoragePercentage(storageInfo)}%</span>
                              <span>剩余 {formatBytes(storageInfo.quota.remaining)}</span>
                            </div>
                          </>
                        ) : (
                          <div style={{ fontSize: '14px', color: '#6d6d70', marginBottom: '16px' }}>
                            <div style={{ marginBottom: '8px' }}>
                              存储类型：{storageInfo.driveType === 'sharepoint' ? 'SharePoint' : 
                                       storageInfo.driveType === 'personal' ? '个人OneDrive' : 
                                       storageInfo.driveType || '云存储'}
                            </div>
                            <div style={{ color: '#8e8e93' }}>
                              存储配额信息不可用
                            </div>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                )}

                {/* 所有账号列表 */}
                <div style={{ marginBottom: '24px' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
                    <Users size={16} color="#6d6d70" />
                    <h4 style={{ fontSize: '14px', fontWeight: '600', margin: 0, color: '#6d6d70' }}>
                      所有账号 ({accounts.length})
                    </h4>
                  </div>

                  {accountsLoading ? (
                    <div style={{ 
                      padding: '20px', 
                      textAlign: 'center', 
                      color: '#6d6d70',
                      fontSize: '14px'
                    }}>
                      正在加载账号列表...
                    </div>
                  ) : accounts.length === 0 ? (
                    <div style={{
                      padding: '20px',
                      backgroundColor: '#f8f9fa',
                      borderRadius: '8px',
                      textAlign: 'center',
                      color: '#6d6d70',
                      fontSize: '14px'
                    }}>
                      暂无其他账号
                    </div>
                  ) : (
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                      {accounts.map((account) => (
                        <div
                          key={account.id}
                          style={{
                            padding: '16px',
                            backgroundColor: account.isActive ? '#e3f2fd' : '#f8f9fa',
                            borderRadius: '8px',
                            border: account.isActive ? '1px solid #007aff' : '1px solid #e1e1e6',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between'
                          }}
                        >
                          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                            <div style={{
                              width: '32px',
                              height: '32px',
                              backgroundColor: account.photo ? 'transparent' : '#007aff',
                              borderRadius: '50%',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              color: 'white',
                              fontSize: '14px',
                              fontWeight: '600',
                              backgroundImage: account.photo ? `url(${account.photo})` : 'none',
                              backgroundSize: 'cover',
                              backgroundPosition: 'center',
                              border: '1px solid #e1e1e6'
                            }}>
                              {!account.photo && account.displayName?.charAt(0)}
                            </div>
                            <div>
                              <div style={{ fontSize: '14px', fontWeight: '600', marginBottom: '2px' }}>
                                {account.displayName}
                                {account.isActive && (
                                  <span style={{ 
                                    marginLeft: '8px', 
                                    fontSize: '12px', 
                                    color: '#007aff',
                                    fontWeight: '500'
                                  }}>
                                    (当前)
                                  </span>
                                )}
                              </div>
                              <div style={{ fontSize: '12px', color: '#6d6d70' }}>
                                {account.mail || account.userPrincipalName}
                              </div>
                              <div style={{ fontSize: '11px', color: '#8e8e93' }}>
                                最后登录：{new Date(account.lastLoginTime).toLocaleString()}
                              </div>
                            </div>
                          </div>
                          
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            {!account.isActive && (
                              <button
                                onClick={() => handleSwitchAccount(account.id)}
                                style={{
                                  padding: '6px 12px',
                                  border: '1px solid #007aff',
                                  borderRadius: '4px',
                                  backgroundColor: 'white',
                                  color: '#007aff',
                                  cursor: 'pointer',
                                  fontSize: '12px',
                                  fontWeight: '500'
                                }}
                              >
                                切换
                              </button>
                            )}
                            <button
                              onClick={() => handleRemoveAccount(account.id, account.displayName)}
                              style={{
                                padding: '6px',
                                border: '1px solid #dc3545',
                                borderRadius: '4px',
                                backgroundColor: 'white',
                                color: '#dc3545',
                                cursor: 'pointer',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}
                              title="删除账号"
                            >
                              <UserX size={14} />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* 操作按钮 */}
                <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
                  <button 
                    onClick={() => loadUserData()}
                    style={{
                      padding: '12px 20px',
                      border: '1px solid #007aff',
                      borderRadius: '6px',
                      backgroundColor: 'white',
                      color: '#007aff',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      fontSize: '14px',
                      fontWeight: '500'
                    }}
                  >
                    <RefreshCw size={16} />
                    刷新信息
                  </button>
                  
                  <button 
                    onClick={() => {
                      // 调试：检查状态同步
                      console.log('=== Account Status Debug Info ===')
                      console.log('AuthStore user:', user)
                      console.log('UserStore profile:', profile)
                      console.log('AccountStore accounts:', accounts)
                      console.log('AccountStore activeAccount:', activeAccount)
                      showMessage('success', '状态调试信息已输出到控制台')
                    }}
                    style={{
                      padding: '12px 20px',
                      border: '1px solid #6c757d',
                      borderRadius: '6px',
                      backgroundColor: 'white',
                      color: '#6c757d',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      fontSize: '14px',
                      fontWeight: '500'
                    }}
                  >
                    <Info size={16} />
                    调试状态
                  </button>
                  
                  <button 
                    onClick={async () => {
                      // 修复状态不一致问题
                      if (user) {
                        console.log('Starting status repair...')
                        console.log('Current AuthStore user:', user)
                        console.log('Account list:', accounts)
                        
                        // 查找匹配的账号（按ID匹配）
                        const matchingAccount = accounts.find(acc => acc.id === user.id)
                        if (matchingAccount) {
                          console.log('Found matching account, switching to:', matchingAccount.displayName)
                          if (!matchingAccount.isActive) {
                            await handleSwitchAccount(matchingAccount.id)
                            showMessage('success', `已将 ${matchingAccount.displayName} 设置为活跃账号`)
                          } else {
                            showMessage('success', '该账号已经是活跃状态')
                          }
                        } else {
                          // 如果在账号列表中找不到当前用户，检查是否有其他匹配方式
                          console.log('No ID match found, trying other matching methods...')
                          const emailMatch = accounts.find(acc => 
                            acc.mail === user.mail || 
                            acc.userPrincipalName === user.userPrincipalName
                          )
                          
                          if (emailMatch) {
                            console.log('Found matching account by email:', emailMatch.displayName)
                            await handleSwitchAccount(emailMatch.id)
                            showMessage('success', `已切换到匹配的账号: ${emailMatch.displayName}`)
                          } else {
                            console.log('No matching account found, forcing state sync...')
                            await syncUserFromActiveAccount()
                            await loadAccounts()
                            await loadUserData()
                            showMessage('success', '已强制同步所有状态')
                          }
                        }
                      } else {
                        showMessage('error', '没有当前用户信息')
                      }
                    }}
                    style={{
                      padding: '12px 20px',
                      border: '1px solid #28a745',
                      borderRadius: '6px',
                      backgroundColor: 'white',
                      color: '#28a745',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      fontSize: '14px',
                      fontWeight: '500'
                    }}
                  >
                    <RefreshCw size={16} />
                    修复状态
                  </button>
                  
                  {accounts.length > 0 && (
                    <button 
                      onClick={handleClearAllAccounts}
                      style={{
                        padding: '12px 20px',
                        border: '1px solid #ff9500',
                        borderRadius: '6px',
                        backgroundColor: 'white',
                        color: '#ff9500',
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        fontSize: '14px',
                        fontWeight: '500'
                      }}
                    >
                      <Trash2 size={16} />
                      清除所有账号
                    </button>
                  )}
                  
                  <button 
                    onClick={handleLogout}
                    style={{
                      padding: '12px 20px',
                      border: '1px solid #dc3545',
                      borderRadius: '6px',
                      backgroundColor: 'white',
                      color: '#dc3545',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      fontSize: '14px',
                      fontWeight: '500'
                    }}
                  >
                    <LogOut size={16} />
                    退出当前账号
                  </button>
                </div>
              </div>
            )}

            {activeTab === 'about' && (
              <div>
                <h3 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '20px' }}>关于 OD-Commander</h3>
                
                <div style={{ textAlign: 'center', marginBottom: '30px' }}>
                  <img 
                    src="/assets/icon.svg" 
                    alt="OD-Commander" 
                    style={{ width: '64px', height: '64px', marginBottom: '16px' }}
                  />
                  <h4 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '8px' }}>
                    OD-Commander
                  </h4>
                  <p style={{ fontSize: '14px', color: '#6d6d70', marginBottom: '4px' }}>
                    版本 1.0.0
                  </p>
                  <p style={{ fontSize: '14px', color: '#6d6d70' }}>
                    现代化的 OneDrive 客户端
                  </p>
                </div>

                <div style={{ fontSize: '14px', lineHeight: '1.6', color: '#1d1d1f' }}>
                  <p>
                    OD-Commander 是一个现代化的第三方 OneDrive 客户端，提供流畅的文件管理体验。
                  </p>
                  <p>
                    基于 Electron + React + TypeScript 构建，支持文件上传、下载、同步等功能。
                  </p>
                </div>

                <div style={{ marginTop: '30px', padding: '16px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
                  <h5 style={{ fontSize: '14px', fontWeight: '600', marginBottom: '8px' }}>
                    技术栈
                  </h5>
                  <ul style={{ fontSize: '13px', color: '#6d6d70', margin: 0, paddingLeft: '20px' }}>
                    <li>Electron 框架</li>
                    <li>React + TypeScript</li>
                    <li>Fluent UI React v9</li>
                    <li>Microsoft Graph API</li>
                    <li>Zustand 状态管理</li>
                  </ul>
                </div>

                <div style={{ marginTop: '20px', padding: '16px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
                  <h5 style={{ fontSize: '14px', fontWeight: '600', marginBottom: '12px' }}>
                    配置管理
                  </h5>
                  <div style={{ fontSize: '13px', color: '#6d6d70', marginBottom: '12px' }}>
                    配置文件位置：{configPath}
                  </div>
                  <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                    <button
                      onClick={handleExportConfig}
                      style={{
                        padding: '6px 12px',
                        border: '1px solid #6c757d',
                        borderRadius: '4px',
                        backgroundColor: 'white',
                        color: '#6c757d',
                        cursor: 'pointer',
                        fontSize: '12px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px'
                      }}
                    >
                      <FileDown size={12} />
                      导出配置
                    </button>
                    <button
                      onClick={handleImportConfig}
                      style={{
                        padding: '6px 12px',
                        border: '1px solid #6c757d',
                        borderRadius: '4px',
                        backgroundColor: 'white',
                        color: '#6c757d',
                        cursor: 'pointer',
                        fontSize: '12px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px'
                      }}
                    >
                      <FileUp size={12} />
                      导入配置
                    </button>
                    <button
                      onClick={handleClearCache}
                      style={{
                        padding: '6px 12px',
                        border: '1px solid #ff9500',
                        borderRadius: '4px',
                        backgroundColor: 'white',
                        color: '#ff9500',
                        cursor: 'pointer',
                        fontSize: '12px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px'
                      }}
                    >
                      <Trash2 size={12} />
                      清理缓存
                    </button>
                  </div>
                </div>

                <div style={{ textAlign: 'center', fontSize: '12px', color: '#8e8e93' }}>
                  配置路径: {configPath}
                </div>
              </div>
            )}
          </div>

          {/* 底部按钮 */}
          <div style={{
            padding: '20px 30px',
            borderTop: '1px solid #e1e1e6',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <button
              onClick={handleReset}
              className="reset-button"
              style={{
                padding: '10px 16px',
                border: '1px solid #ff3b30',
                borderRadius: '6px',
                backgroundColor: 'white',
                color: '#ff3b30',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                gap: '6px'
              }}
            >
              <RotateCcw size={14} />
              重置
            </button>

            <div style={{ display: 'flex', gap: '12px' }}>
              <button
                onClick={onClose}
                style={{
                  padding: '8px 20px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  backgroundColor: 'white',
                  color: '#1d1d1f',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                style={{
                  padding: '8px 20px',
                  border: 'none',
                  borderRadius: '6px',
                  backgroundColor: '#007aff',
                  color: 'white',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
              >
                <Save size={14} />
                Save
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Settings