/* 简约现代化全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  background: #ffffff;
  color: #1d1d1f;
  line-height: 1.5;
  font-size: 14px;
  overflow: hidden;
}

/* 改善滚动体验 */
.scrollable {
  overflow-y: auto;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

.scrollable::-webkit-scrollbar {
  width: 8px;
}

.scrollable::-webkit-scrollbar-track {
  background: #f5f5f7;
  border-radius: 4px;
}

.scrollable::-webkit-scrollbar-thumb {
  background: #c7c7cc;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.scrollable::-webkit-scrollbar-thumb:hover {
  background: #a1a1a6;
}

.scrollable::-webkit-scrollbar-thumb:active {
  background: #8e8e93;
}

/* 简约按钮样式 */
.modern-button {
  background: #007aff;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  user-select: none;
}

.modern-button:hover {
  background: #0056cc;
  transform: translateY(-1px);
}

.modern-button:active {
  transform: translateY(0);
}

.modern-button:disabled {
  background: #f2f2f7;
  color: #8e8e93;
  cursor: not-allowed;
  transform: none;
}

/* 按钮变体 */
.modern-button.secondary {
  background: #f2f2f7;
  color: #1d1d1f;
}

.modern-button.secondary:hover {
  background: #e5e5ea;
}

.modern-button.success {
  background: #34c759;
}

.modern-button.success:hover {
  background: #28a745;
}

.modern-button.danger {
  background: #ff3b30;
}

.modern-button.danger:hover {
  background: #d70015;
}

/* 简约卡片样式 */
.modern-card {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e5e7;
  transition: all 0.2s ease;
}

.modern-card:hover {
  border-color: #d1d1d6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 简约输入框 */
.modern-input {
  background: #ffffff;
  border: 1px solid #d1d1d6;
  border-radius: 8px;
  padding: 10px 12px;
  font-size: 14px;
  transition: all 0.2s ease;
  outline: none;
  font-family: inherit;
}

.modern-input:focus {
  border-color: #007aff;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

/* 简约复选框 */
.modern-checkbox {
  appearance: none;
  width: 18px;
  height: 18px;
  border: 1px solid #d1d1d6;
  border-radius: 4px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.modern-checkbox:checked {
  background: #007aff;
  border-color: #007aff;
}

.modern-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 11px;
  font-weight: 600;
}

/* 简约进度条 */
.modern-progress {
  background: #f2f2f7;
  border-radius: 4px;
  overflow: hidden;
  height: 4px;
}

.modern-progress-bar {
  background: #007aff;
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* 简约表格样式 */
.modern-table {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e5e7;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modern-table-header {
  background: #f9f9f9;
  font-weight: 600;
  color: #6d6d70;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e5e7;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  flex-shrink: 0;
}

.modern-table-body {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.modern-table-row {
  padding: 12px 16px;
  border-bottom: 1px solid #f2f2f7;
  transition: background-color 0.15s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  min-height: 48px;
}

.modern-table-row:hover {
  background: #f9f9f9;
}

.modern-table-row.selected {
  background: #e3f2fd;
}

.modern-table-row:last-child {
  border-bottom: none;
}

/* 简约侧边栏 */
.modern-sidebar {
  background: #f9f9f9;
  border-right: 1px solid #e5e5e7;
  display: flex;
  flex-direction: column;
}

.modern-sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.modern-sidebar-item {
  padding: 10px 16px;
  margin: 2px 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.15s ease;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 10px;
  color: #1d1d1f;
  user-select: none;
}

.modern-sidebar-item:hover {
  background: #e5e5ea;
}

.modern-sidebar-item.active {
  background: #007aff;
  color: white;
}

/* 简约头部 */
.modern-header {
  background: #ffffff;
  border-bottom: 1px solid #e5e5e7;
  flex-shrink: 0;
}

/* 简约模态框 */
.modern-modal {
  background: rgba(0, 0, 0, 0.4);
  animation: fadeIn 0.2s ease;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modern-modal-content {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  animation: slideUp 0.3s ease;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: scale(0.95);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}

/* 简约工具栏 */
.modern-toolbar {
  background: #ffffff;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  border: 1px solid #e5e5e7;
  flex-shrink: 0;
}

/* 简约状态栏 */
.modern-status-bar {
  background: #f9f9f9;
  border-top: 1px solid #e5e5e7;
  padding: 8px 16px;
  font-size: 12px;
  color: #6d6d70;
  flex-shrink: 0;
}

/* 简约加载动画 */
.modern-loading {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #e5e5e7;
  border-radius: 50%;
  border-top-color: #007aff;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 简约文件图标 */
.modern-file-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 14px;
  background: #f2f2f7;
  color: #6d6d70;
  flex-shrink: 0;
}

/* 消息样式 */
.message-success {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
  border-radius: 8px;
}

.message-error {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  border-radius: 8px;
}

/* 文件名样式 */
.file-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
}

/* 路径导航样式 */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.breadcrumb-item {
  cursor: pointer;
  color: #007aff;
  font-weight: 500;
  white-space: nowrap;
  transition: color 0.2s ease;
}

.breadcrumb-item:hover {
  color: #0056cc;
}

.breadcrumb-item.current {
  color: #1d1d1f;
  cursor: default;
}

.breadcrumb-separator {
  color: #6d6d70;
  margin: 0 4px;
  user-select: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-button {
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .modern-sidebar-item {
    padding: 8px 12px;
  }
  
  .modern-table-row {
    padding: 8px 12px;
    min-height: 44px;
  }
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f5f5f7;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c7c7cc;
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a6;
}

::-webkit-scrollbar-corner {
  background: #f5f5f7;
}

/* 选择文本样式 */
::selection {
  background: rgba(0, 122, 255, 0.2);
  color: #1d1d1f;
}

/* 焦点样式 */
:focus-visible {
  outline: 2px solid #007aff;
  outline-offset: 2px;
}

/* 图标动画 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
} 