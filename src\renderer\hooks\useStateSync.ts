import { useCallback } from 'react'
import { useAuthStore } from '../store/authStore'
import { useUserStore } from '../store/userStore'
import { useAccountStore } from '../store/accountStore'

/**
 * 统一状态同步Hook
 * 负责协调前端各个store之间的状态同步
 */
export const useStateSync = () => {
  const { setUser, logout: authLogout } = useAuthStore()
  const { loadUserData, loadUserPhoto, clearUserData } = useUserStore()
  const { loadAccounts, loadActiveAccount, refreshAccounts } = useAccountStore()

  /**
   * 执行完整的状态同步
   * 从后端获取最新状态并更新所有前端store
   */
  const syncAllStates = useCallback(async (): Promise<boolean> => {
    try {
      console.log('Starting complete state sync...')

      // 1. 触发后端状态同步
      const backendSyncResult = await window.electronAPI.state.syncAll()
      if (!backendSyncResult.success) {
        console.error('Backend state sync failed:', backendSyncResult.error)
        return false
      }

      // 2. 获取后端的当前状态
      const stateResult = await window.electronAPI.state.getCurrent()
      if (!stateResult.success) {
        console.error('Failed to get current state:', stateResult.error)
        return false
      }

      const currentState = stateResult.state
      console.log('Current backend state:', currentState)

      // 3. 根据后端状态更新前端stores
      if (currentState.isAuthenticated && currentState.activeAccount) {
        // 有活跃账户，更新认证状态
        setUser(
          {
            id: currentState.activeAccount.id,
            displayName: currentState.activeAccount.displayName,
            userPrincipalName: currentState.activeAccount.userPrincipalName,
            mail: currentState.activeAccount.mail
          },
          '' // accessToken由后端管理，前端不需要存储
        )

        // 加载用户数据
        await loadUserData()
        await loadUserPhoto()
      } else {
        // 没有活跃账户，清除认证状态
        await authLogout()
        clearUserData()
      }

      // 4. 刷新账户列表
      await refreshAccounts()

      console.log('Complete state sync finished successfully')
      return true
    } catch (error) {
      console.error('Failed to sync all states:', error)
      return false
    }
  }, [setUser, authLogout, loadUserData, loadUserPhoto, clearUserData, refreshAccounts])

  /**
   * 同步认证状态
   * 根据后端的活跃账户更新前端认证状态
   */
  const syncAuthState = useCallback(async (): Promise<boolean> => {
    try {
      console.log('Syncing auth state...')

      const activeAccountResult = await window.electronAPI.accounts.getActive()
      if (!activeAccountResult.success) {
        console.error('Failed to get active account:', activeAccountResult.error)
        return false
      }

      if (activeAccountResult.account) {
        // 有活跃账户，更新认证状态
        setUser(
          {
            id: activeAccountResult.account.id,
            displayName: activeAccountResult.account.displayName,
            userPrincipalName: activeAccountResult.account.userPrincipalName,
            mail: activeAccountResult.account.mail
          },
          '' // accessToken由后端管理
        )
        console.log('Auth state synced with active account:', activeAccountResult.account.displayName)
      } else {
        // 没有活跃账户，清除认证状态
        await authLogout()
        console.log('Auth state cleared - no active account')
      }

      return true
    } catch (error) {
      console.error('Failed to sync auth state:', error)
      return false
    }
  }, [setUser, authLogout])

  /**
   * 同步用户数据
   * 重新加载用户资料和头像
   */
  const syncUserData = useCallback(async (): Promise<boolean> => {
    try {
      console.log('Syncing user data...')
      
      await loadUserData()
      await loadUserPhoto()
      
      console.log('User data synced successfully')
      return true
    } catch (error) {
      console.error('Failed to sync user data:', error)
      return false
    }
  }, [loadUserData, loadUserPhoto])

  /**
   * 同步账户列表
   * 重新加载账户列表和活跃账户
   */
  const syncAccountData = useCallback(async (): Promise<boolean> => {
    try {
      console.log('Syncing account data...')
      
      await refreshAccounts()
      
      console.log('Account data synced successfully')
      return true
    } catch (error) {
      console.error('Failed to sync account data:', error)
      return false
    }
  }, [refreshAccounts])

  /**
   * 登录后的状态同步
   * 在用户登录成功后调用，确保所有状态都正确更新
   */
  const syncAfterLogin = useCallback(async (): Promise<boolean> => {
    try {
      console.log('Syncing state after login...')
      
      // 执行完整的状态同步
      const success = await syncAllStates()
      
      if (success) {
        console.log('Post-login state sync completed successfully')
      } else {
        console.error('Post-login state sync failed')
      }
      
      return success
    } catch (error) {
      console.error('Failed to sync state after login:', error)
      return false
    }
  }, [syncAllStates])

  /**
   * 切换账户后的状态同步
   * 在用户切换账户后调用，确保所有状态都正确更新
   */
  const syncAfterAccountSwitch = useCallback(async (): Promise<boolean> => {
    try {
      console.log('Syncing state after account switch...')
      
      // 执行完整的状态同步
      const success = await syncAllStates()
      
      if (success) {
        console.log('Post-switch state sync completed successfully')
      } else {
        console.error('Post-switch state sync failed')
      }
      
      return success
    } catch (error) {
      console.error('Failed to sync state after account switch:', error)
      return false
    }
  }, [syncAllStates])

  /**
   * 退出登录后的状态同步
   * 在用户退出登录后调用，确保所有状态都正确清理
   */
  const syncAfterLogout = useCallback(async (): Promise<boolean> => {
    try {
      console.log('Syncing state after logout...')
      
      // 清除前端状态
      await authLogout()
      clearUserData()
      await refreshAccounts()
      
      console.log('Post-logout state sync completed successfully')
      return true
    } catch (error) {
      console.error('Failed to sync state after logout:', error)
      return false
    }
  }, [authLogout, clearUserData, refreshAccounts])

  /**
   * 重置所有状态到一致状态
   * 在出现严重状态不一致时调用
   */
  const resetAllStates = useCallback(async (): Promise<boolean> => {
    try {
      console.log('Resetting all states...')
      
      // 重置后端状态
      const resetResult = await window.electronAPI.state.reset()
      if (!resetResult.success) {
        console.error('Failed to reset backend state:', resetResult.error)
        return false
      }
      
      // 清除前端状态
      await authLogout()
      clearUserData()
      await refreshAccounts()
      
      console.log('All states reset successfully')
      return true
    } catch (error) {
      console.error('Failed to reset all states:', error)
      return false
    }
  }, [authLogout, clearUserData, refreshAccounts])

  return {
    syncAllStates,
    syncAuthState,
    syncUserData,
    syncAccountData,
    syncAfterLogin,
    syncAfterAccountSwitch,
    syncAfterLogout,
    resetAllStates
  }
}
